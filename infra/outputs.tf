output "auth0_client_id" {
  value = module.auth0.monarch_web_client_id
  description = "The client ID for the Monarch Web application"
}

output "qivdb_connection_name" {
  value = module.auth0.qivdb_connection_name
  description = "The name of the QIVDB connection"
}

output "guardian_policy" {
  value = module.auth0.guardian_policy
  description = "The Guardian MFA policy"
}

output "webauthn_enabled" {
  value = module.auth0.webauthn_enabled
  description = "WebAuthn/Passkey authentication status (disabled)"
}

output "tenant_flags" {
  value = module.auth0.tenant_flags
  description = "Tenant configuration flags"
}

output "connection_settings" {
  value = module.auth0.connection_settings
  description = "Database connection settings"
}

output "mfa_configuration" {
  value = module.auth0.mfa_configuration
  description = "MFA granular control configuration"
}

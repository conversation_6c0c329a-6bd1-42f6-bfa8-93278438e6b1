exports.onExecutePostLogin = async (event, api) => {
  const { user, client, request } = event;

  // Configuration from Action secrets
  const MFA_CLIENT_IDS = event.secrets.MFA_CLIENT_IDS || '';
  const MFA_USER_EMAILS = event.secrets.MFA_USER_EMAILS || '';

  // Parse comma-separated lists
  const mfaClientIds = MFA_CLIENT_IDS ? MFA_CLIENT_IDS.split(',').map(id => id.trim()) : [];
  const mfaUserEmails = MFA_USER_EMAILS ? MFA_USER_EMAILS.split(',').map(email => email.trim().toLowerCase()) : [];

  // Helper function to check if MFA should be required
  function shouldRequireMFA() {
    // If both arrays are empty, require MFA for all
    if (mfaClientIds.length === 0 && mfaUserEmails.length === 0) {
      return true;
    }

    // Check if current client requires MFA
    const clientRequiresMFA = mfaClientIds.length === 0 || mfaClientIds.includes(client.client_id);

    // Check if current user requires MFA
    const userRequiresMFA = mfaUserEmails.length === 0 || mfaUserEmails.includes(user.email.toLowerCase());

    // Require MFA if both client and user conditions are met
    return clientRequiresMFA && userRequiresMFA;
  }

  // Skip MFA for machine-to-machine flows
  if (event.authorization && event.authorization.grantType === 'client_credentials') {
    return;
  }

  // Skip if user doesn't have email
  if (!user.email) {
    return;
  }

  // Check if MFA should be required
  if (shouldRequireMFA()) {
    // Check if user has already completed MFA in this session
    if (!event.authentication || !event.authentication.methods.find(method =>
      method.name === 'mfa'
    )) {
      // Require MFA (OTP only, no passkey support)
      api.multifactor.enable('any', { allowRememberBrowser: false });

      // Add metadata for monitoring
      api.user.setAppMetadata('mfa_required_by', 'granular_policy');
      api.user.setAppMetadata('mfa_required_at', new Date().toISOString());
    }
  }

  // Add user metadata for audit purposes
  api.user.setAppMetadata('last_login_client', client.client_id);
  api.user.setAppMetadata('last_login_at', new Date().toISOString());
};

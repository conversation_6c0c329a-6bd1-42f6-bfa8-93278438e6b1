<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>Monarch</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>
<body>

  <!--[if IE 8]>
  <script src="//cdnjs.cloudflare.com/ajax/libs/ie8/0.2.5/ie8.js"></script>
  <![endif]-->

  <!--[if lte IE 9]>
  <script src="https://cdn.auth0.com/js/base64.js"></script>
  <script src="https://cdn.auth0.com/js/es5-shim.min.js"></script>
  <![endif]-->

  <script src="https://cdn.auth0.com/js/auth0/9.5.1/auth0.min.js"></script>
  <script src="https://cdn.auth0.com/js/lock/11.11/lock.min.js"></script>
  <script>
    // Parse Authorization Parameters
    var config = JSON.parse(decodeURIComponent(escape(window.atob('@@config@@'))));
    
    //Strip 'login.' from front of custom domain to determine Monarch host
    var monarchHost = config.auth0Domain.substring(6);
  
    // Apply Monarch styling - load stylesheet from Monarch
    var ss = document.createElement("link");
    ss.rel = "stylesheet";
    ss.type = "text/css";
    ss.href = "https://" + monarchHost +
      "/assets/stylesheets/property/snapLarge.css";
    document.getElementsByTagName("head")[0].appendChild(ss);  

    // Configure Auth0 Lock widget
    var lock = new Auth0Lock(config.clientID, config.auth0Domain, {
      auth: {
        redirectUrl: config.callbackURL,
        responseType: config.internalOptions.response_type,
        params: config.internalOptions
      },
      /* additional configuration needed for custom domains */
      configurationBaseUrl: config.clientConfigurationBaseUrl,
      overrides: {
        __tenant: config.auth0Tenant,
        __token_issuer: config.authorizationServer.issuer
      }, 
      theme: {
        logo: "https://" + monarchHost + "/assets/images/monarchLogo-login.png"
      },
      languageDictionary: {
        enterpriseActiveLoginInstructions: "Please enter your QV network " + 
          "username and password (not your email address for %s).",
        windowsAuthInstructions: 'You are connected from the QV network&hellip;'
      }
    });

    // Helper to get a querystring value.
    function getParameterByName( name ){
      name = name.replace(/[\[]/,"\\\[").replace(/[\]]/,"\\\]");
      var regexS = "[\\?&]"+name+"=([^&#]*)";
      var regex = new RegExp( regexS );
      var results = regex.exec( window.location.href );
      if( results == null )
        return "";
      else
        return decodeURIComponent(results[1].replace(/\+/g, " "));
    }

    // Initialise Auth0 Authentication API
    var auth = new auth0.Authentication({
      domain: config.auth0Domain,
      clientID: config.clientID,
      overrides: {
          __tenant: config.auth0Tenant,
          __token_issuer: config.authorizationServer.issuer
      }
    });

    // Initialise Auth0 WebAuth API
    var webAuth = new auth0.WebAuth({
      domain: config.auth0Domain,
      clientID: config.clientID,
      overrides: {
          __tenant: config.auth0Tenant,
          __token_issuer: config.authorizationServer.issuer
      }
    });

    /*
     * Verify if Kerberos is possible (based on the IP address).
     * If it is, try to authenticate the user silently using 
     * Windows Integrated Authentication.
     */
    auth.getSSOData(true, function(err, data) {
      if (!err) {
        if (data.connection && data.strategy === 'ad' && !config.prompt) {
          // Windows Integrated Authentication
          webAuth.authorize({
            connection: data.connection,
            state: getParameterByName('state'),
            protocol: getParameterByName('protocol') || 'oauth2',
            scope: getParameterByName('scope') || 'openid',
            responseType: (config.internalOptions || {}).response_type ||
              (config.callbackOnLocationHash ? 'token' : 'code'),
            redirectUri: config.callbackURL
          });
        } else {
          // Authenticate via Lock widget
          lock.show();
        }
      }
    });    
  </script>
</body>
</html>